<template>
  <div class="second-page-container">
    <div class="second-content" ref="secondContent">
      <h1 ref="textLine1">广东热爱云居科技有限公司，致力基于产业互联和泛家居的数字化、泛家居</h1>
      <h1 ref="textLine2">设计软件研发与应用，泛家居新零售S2B2C平台运营、品牌商业运营、大数据</h1>
      <h1 ref="textLine3">分析及应用,行业投资及金融服务，热爱科技用数字化赋能泛家居的企业转型升</h1>
      <h1 ref="textLine4">级，用科技提高泛家居企业的智造能力和经营效率，提供优秀的品牌用户体验</h1>
      <h1 ref="textLine5">环境和消费服务，搭建全球高端泛家居供应链平台，热爱科技立足于全球最大</h1>
      <h1 ref="textLine6">的家居制造中心-广东东莞，心怀为人们提供健康、舒适、智慧的美好家居生活</h1>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 引用DOM元素
const secondContent = ref<HTMLElement>()
const textLine1 = ref<HTMLElement>()
const textLine2 = ref<HTMLElement>()
const textLine3 = ref<HTMLElement>()
const textLine4 = ref<HTMLElement>()
const textLine5 = ref<HTMLElement>()
const textLine6 = ref<HTMLElement>()

// 滚动处理函数
const handleScroll = () => {
  const windowHeight = window.innerHeight
  const windowCenter = windowHeight / 2

  // 处理每一行文字的颜色渐变
  const textRefs = [textLine1, textLine2, textLine3, textLine4, textLine5, textLine6]

  textRefs.forEach(ref => {
    if (!ref.value) return

    const elementRect = ref.value.getBoundingClientRect()
    const elementTop = elementRect.top
    const elementBottom = elementRect.bottom
    const elementCenter = (elementTop + elementBottom) / 2

    // 计算元素距离屏幕中心的距离
    const distanceFromCenter = Math.abs(elementCenter - windowCenter)
    const maxDistance = windowHeight / 2 // 最大距离为屏幕高度的一半

    // 计算渐变进度 (0 到 1)
    // 当元素在屏幕中心时，progress = 1 (完全变色)
    // 当元素距离屏幕中心最远时，progress = 0 (保持灰色)
    let progress = 0

    // 只有当元素在视窗内时才进行渐变
    if (elementBottom > 0 && elementTop < windowHeight) {
      progress = Math.max(0, 1 - (distanceFromCenter / maxDistance))

      // 增强渐变效果，让变化更明显
      progress = Math.pow(progress, 0.8)
    }

    // 计算颜色值
    const grayColor = { r: 220, g: 220, b: 220 } // 很浅的灰色 #dcdcdc
    const targetColor = { r: 107, g: 117, b: 229 } // 目标颜色 #6b75e5

    const currentR = Math.round(grayColor.r + (targetColor.r - grayColor.r) * progress)
    const currentG = Math.round(grayColor.g + (targetColor.g - grayColor.g) * progress)
    const currentB = Math.round(grayColor.b + (targetColor.b - grayColor.b) * progress)

    const currentColor = `rgb(${currentR}, ${currentG}, ${currentB})`

    // 应用颜色到当前文本行
    ref.value.style.color = currentColor
  })
}

onMounted(() => {
  // 添加滚动监听器到父容器和window
  const pageContainer = document.querySelector('.page-container')
  if (pageContainer) {
    pageContainer.addEventListener('scroll', handleScroll)
  }

  // 也监听window的滚动事件，以防万一
  window.addEventListener('scroll', handleScroll)

  // 初始化颜色
  handleScroll()
})

onUnmounted(() => {
  // 移除滚动监听器
  const pageContainer = document.querySelector('.page-container')
  if (pageContainer) {
    pageContainer.removeEventListener('scroll', handleScroll)
  }

  window.removeEventListener('scroll', handleScroll)
})
</script>
<style scoped>
.second-page-container {
    background: url("../assets/second_background.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    width: calc(100% - 48px);
    min-height: 100vh;
    margin: 0 24px 0 24px;
    border-radius: 0 0 20px 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.second-content {
    text-align: center;
    padding: 40px;
}

.second-content h1 {
    font-family: "Alibaba PuHuiTi 2.0";
    font-size: 32px;
    font-style: normal;
    font-weight: 900;
    line-height: 64px;
    letter-spacing: 0.64px;
    color: #eeeeee; /* 初始很浅的灰色，几乎看不清 */
    transition: color 0.1s ease-out; /* 平滑过渡效果 */
    margin: 20px 0;
    line-height: 1.5;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .second-page-container {
    background: url("../assets/second_background.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    width: calc(100% - 24px);
    min-height: auto;
    margin: 40px 12px 0 12px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
  }

  .second-content {
    padding: 0 12px;
    text-align: left;
  }

  .second-content h1 {
    color: #5661E1;
    font-family: "Alibaba PuHuiTi 2.0";
    font-size: 17px;
    font-style: normal;
    font-weight: 900;
    line-height: 2.2;
    letter-spacing: 1.5px;
    margin: 0;
    display: inline;
    transition: color 0.1s ease-out;
    color: var(---, #5661E1);
  }
}

/* 小屏幕手机适配 */
@media (max-width: 480px) {
  .second-page-container {
    margin: 32px 8px 0 8px;
    width: calc(100% - 16px);
    border-radius: 16px;
    padding: 32px 0;
  }

  .second-content {
    padding: 0 8px;
  }

  .second-content h1 {
    font-size: 15px;
    line-height: 2.0;
    letter-spacing: 1.2px;
    word-spacing: 1.5px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
  .second-page-container {
    margin: 28px 6px 0 6px;
    width: calc(100% - 12px);
    border-radius: 12px;
    padding: 28px 0;
  }

  .second-content {
    padding: 0 6px;
  }

  .second-content h1 {
    font-size: 14px;
    line-height: 1.8;
    letter-spacing: 1.0px;
    word-spacing: 1px;
  }
}
</style>