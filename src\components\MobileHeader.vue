<template>
  <!-- 移动端顶部白色区域 -->
  <div class="mobile-header" v-if="isMobile">
    <div class="mobile-header-top">
      <div class="mobile-brand">
        <img src="../assets/logo.svg" alt="logo" class="mobile-logo" />
      </div>
      <div class="mobile-menu" @click="toggleMobileMenu">
        <div class="menu-dots" :class="{ 'menu-expanded': showMobileMenu }">
          <img :src="showMobileMenu ? '/src/assets/top_nounfold.png' : '/src/assets/top_unfold.png'" width="20px" height="20px" />
        </div>
      </div>
    </div>
    <!-- 展开的菜单项 -->
    <div class="mobile-menu-items" v-if="showMobileMenu">
      <div class="mobile-menu-item" @click="handleMobileLogin">登录</div>
      <div class="mobile-menu-item" @click="handleMobileRegister">注册</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 移动端相关状态
const isMobile = ref(false)
const showMobileMenu = ref(false)

// 移动端相关方法
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const closeMobileMenu = () => {
  showMobileMenu.value = false
}

const handleMobileLogin = () => {
  router.push('/login')
  closeMobileMenu()
}

const handleMobileRegister = () => {
  router.push('/register')
  closeMobileMenu()
}

// 监听窗口大小变化
const handleResize = () => {
  checkMobile()
  if (!isMobile.value) {
    showMobileMenu.value = false
  }
}

// 组件挂载时的初始化
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', handleResize)
})

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 暴露给父组件的方法和状态
defineExpose({
  isMobile,
  showMobileMenu,
  closeMobileMenu
})
</script>

<style scoped>
/* 移动端白色顶部区域 */
.mobile-header {
  background: white;
  margin: 12px 12px 0 12px;
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.mobile-header-top {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.mobile-brand {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mobile-logo {
  height: 32px;
  width: auto;
}

.mobile-menu {
  cursor: pointer;
  padding: 8px;
}

.menu-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.menu-dots img {
  transition: all 0.3s ease;
}

/* 移动端展开菜单样式 */
.mobile-menu-items {
  padding: 0 20px 20px 20px;
  border-top: 1px solid #f0f0f0;
}

.mobile-menu-item {
  padding: 15px 0;
  font-size: 16px;
  color: #333;
  cursor: pointer;
  text-align: left;
  border-bottom: 1px solid #f5f5f5;
}

.mobile-menu-item:last-child {
  border-bottom: none;
}

.mobile-menu-item:hover {
  color: #2F7DFB;
}

/* 小屏幕手机适配 */
@media (max-width: 480px) {
  .mobile-header {
    margin: 8px 8px 0 8px;
    border-radius: 16px 16px 0 0;
  }

  .mobile-header-top {
    height: 72px;
    padding: 0 16px;
  }

  .mobile-logo {
    height: 28px;
  }

  .mobile-menu-items {
    padding: 0 16px 16px 16px;
  }

  .mobile-menu-item {
    padding: 12px 0;
    font-size: 15px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
  .mobile-header {
    margin: 6px 6px 0 6px;
    border-radius: 12px 12px 0 0;
  }

  .mobile-header-top {
    height: 64px;
    padding: 0 12px;
  }

  .mobile-logo {
    height: 24px;
  }

  .mobile-menu-items {
    padding: 0 12px 12px 12px;
  }

  .mobile-menu-item {
    padding: 10px 0;
    font-size: 14px;
  }
}
</style>
