import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import IndexView from './views/index.vue'
import LoginView from './views/Login.vue'
import RegisterView from './views/Register.vue'
import ForgetPwdPage from './views/ForgetPwdPage.vue'
import LeaseAgreement from './views/LeaseAgreement.vue'
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';

// 添加全局CSS来隐藏滚动条
const globalStyle = document.createElement('style');
globalStyle.textContent = `
  /* 隐藏所有滚动条 */
  * {
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }

  *::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
  }

  html, body {
    overflow-x: hidden !important;
  }
`;
document.head.appendChild(globalStyle);

const routes = [
  {
    path: '/',
    name: 'Home',
    component: IndexView
  },
  {
    path: '/login',
    name: 'Login',
    component: LoginView
  },
  {
    path: '/register',
    name: 'Register',
    component: RegisterView
  },
  {
    path: '/forgetPassword',
    name: 'ForgetPwdPage',
    component: ForgetPwdPage,
  },
  {
    path: '/leaseagreement',
    name: 'LeaseAgreement',
    component: LeaseAgreement,
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

const app = createApp(App)
app.use(ElementPlus)
app.use(router)
app.mount('#app')
