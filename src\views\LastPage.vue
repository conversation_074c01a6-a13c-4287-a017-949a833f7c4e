<template>
  <div class="last-page-container">
    <div class="end-background">
      <img src="../assets/deeplove_brand.svg" class="end-bg" alt="end_background"/>
      <h2 class="title">一起开启数字体验旅程</h2>
      <span>Let’s start our digital experience journey together</span>
      <el-button type="primary" size="large" class="experience-btn">
        立即体验
      </el-button>
    </div>
    <div class="corporate-information">
      <div class="copyright-line">Copyright © 2023粤ICP备13047170号-3</div>
      <div class="address-line">地址：中国广东省东莞市东城区牛山堑头新兴工业区</div>
    </div>
  </div>
</template>

<script setup lang="ts">

</script>

<style scoped>
.last-page-container {
  width: calc(100% - 48px);
  height: 553px;
  margin: 0px 24px 0px 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 20px;
}

.end-background {
  background-image: url("../assets/last_background.png");
  height: 496px;
  width: 100%;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 120px;
  box-sizing: border-box;
}

.end-background > span {
  color: rgba(255, 255, 255, 0.20);
  text-align: center;
  font-family: Arial;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 2.38px;
  text-transform: uppercase;
  margin-bottom: 32px;
}

.end-bg {
  display: block;
  margin-bottom: 32px;
}

.title {
  background: linear-gradient(180deg, #FFF 0%, rgba(255, 255, 255, 0.50) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: center;
  font-family: "江城斜黑体", sans-serif;
  font-size: 48px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
  letter-spacing: 7.68px;
  margin: 0 0 16px 0;
}

.experience-btn {
  font-size: 16px;
  padding: 16px 40px;
  background: #1e69e2; /* 按钮背景色 */
  border: none;
  border-radius: 12px;
  font-family: "江城斜黑体", sans-serif;
  font-weight: 900;
  min-width: 154px;
  width: 154px;
  height: 48px;
}

.experience-btn:hover {
  background: #1e69e2 !important;
  border: none !important;
  transform: none !important;
  box-shadow: none !important;
}

.corporate-information{
  padding-top: 0;
  width: 519px;
  height: 57px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.copyright-line,
.address-line {
  color: var(--text-4, #C9CDD4);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-align: center;
}

.copyright-line {
  margin-right: 2em;
}

/* 桌面端显示在同一行 */
@media (min-width: 769px) {
  .corporate-information {
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
}

@media (max-width: 768px) {
  .last-page-container {
    height: auto;
    min-height: 60vh;
    margin: 0 3vw;
    padding-top: 3vh;
    width: calc(100% - 6vw);
  }

  .title {
    background: linear-gradient(180deg, #FFF 0%, rgba(255, 255, 255, 0.50) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
    font-family: "江城斜黑体", sans-serif;
    font-size: 24px;
    font-style: normal;
    font-weight: 900;
    line-height: normal;
    letter-spacing: 3.84px;
    margin-bottom: 8px;
  }

  .end-background {
    background-image: url("../assets/last_background.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding-top: 8vh;
    height: auto;
    min-height: 50vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  .end-bg {
    width: 15vw;
    height: auto;
    margin-bottom: 4vh;
  }

  .end-background > span {
    color: rgba(255, 255, 255, 0.20);
    text-align: center;
    font-family: Arial;
    font-size: 2vw;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 0.5vw;
    text-transform: uppercase;
    margin-bottom: 4vh;
  }

  .corporate-information {
    width: 90vw;
    height: auto;
    padding: 2vh 0;
    flex-direction: column;
    gap: 1vh;
  }

  .copyright-line,
  .address-line {
    color: var(--text-4, #C9CDD4);
    font-family: "Alibaba PuHuiTi 2.0";
    font-size: 2.5vw;
    font-style: normal;
    font-weight: 400;
    line-height: 1.4;
    text-align: center;
    white-space: normal;
    word-break: break-all;
  }
}

</style>