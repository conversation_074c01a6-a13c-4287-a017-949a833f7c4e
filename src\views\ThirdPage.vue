<template>
  <div class="third-page-container">
    <div class="cards-grid">
      <div class="card">
        <div class="card-content">
          <h3 class="card-title">业务中台</h3>
          <p class="card-subtitle">共享复用，让业务更便捷</p>
        </div>
        <div class="card-image">
          <img src="../assets/gridding.png" alt="业务中台" />
        </div>
        <div class="power-icon">
          <img src="../assets/SVG/business_center.svg" class="power-svg" alt="业务中台图标"/>
          <img src="../assets/GIF/business_center.gif" class="power-gif" alt="业务中台动画"/>
        </div>
      </div>

      <div class="card">
        <div class="card-content">
          <h3 class="card-title">数据中台</h3>
          <p class="card-subtitle">用好数据，让业务更智能</p>
        </div>
        <div class="card-image">
          <img src="../assets/gridding.png" alt="数据中台" />
        </div>
        <div class="power-icon">
          <img src="../assets/SVG/data_middle_platform.svg" class="power-svg" alt="数据中台图标"/>
          <img src="../assets/GIF/data_middle_platform.gif" class="power-gif" alt="数据中台动画"/>
        </div>
      </div>

      <div class="card">
        <div class="card-content">
          <h3 class="card-title">应用开发平台</h3>
          <p class="card-subtitle">IT业务融合，快速创新</p>
        </div>
        <div class="card-image">
          <img src="../assets/gridding.png" alt="应用开发平台" />
        </div>
        <div class="power-icon">
          <img src="../assets/SVG/App_development_platform.svg" class="power-svg" alt="应用开发平台图标"/>
          <img src="../assets/GIF/App_development_platform.gif" class="power-gif" alt="应用开发平台动画"/>
        </div>
      </div>

      <div class="card">
        <div class="card-content">
          <h3 class="card-title">大数据开发平台</h3>
          <p class="card-subtitle">激发数据潜能，实现业务智能</p>
        </div>
        <div class="card-image">
          <img src="../assets/gridding.png" alt="大数据开发平台" />
        </div>
        <div class="power-icon">
          <img src="../assets/SVG/big_data_platform.svg" class="power-svg" alt="大数据开发平台图标"/>
          <img src="../assets/GIF/big_data_platform.gif" class="power-gif" alt="大数据开发平台动画"/>
        </div>
      </div>

      <div class="card">
        <div class="card-content">
          <h3 class="card-title">研发服务平台</h3>
          <p class="card-subtitle">让敏捷贯穿创新全流程</p>
        </div>
        <div class="card-image">
          <img src="../assets/gridding.png" alt="研发服务平台" />
        </div>
        <div class="power-icon">
          <img src="../assets/SVG/research_and_development.svg" class="power-svg" alt="研发服务平台图标"/>
          <img src="../assets/GIF/research_and_development.gif" class="power-gif" alt="研发服务平台动画"/>
        </div>
      </div>

      <div class="card">
        <div class="card-content">
          <h3 class="card-title">应用市场</h3>
          <p class="card-subtitle">生态助力能力构建</p>
        </div>
        <div class="card-image">
          <img src="../assets/gridding.png" alt="应用市场" />
        </div>
        <div class="power-icon">
          <img src="../assets/SVG/app_market.svg" class="power-svg" alt="应用市场图标"/>
          <img src="../assets/GIF/app_market.gif" class="power-gif" alt="应用市场动画"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

</script>

<style scoped>
.third-page-container {
  width: calc(100% - 48px);
  margin: 120px 24px;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(3, 464px);
  grid-template-rows: repeat(2, 200px);
  gap: 24px;
  justify-content: center;
  margin: 0 auto;
}

.card {
  /* background: #f8f9fa; */
  border-radius: 20px;
  padding: 24px;
  position: relative;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
  width: 464px;
  height: 200px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  box-sizing: border-box;
  border: 1px solid var(--border-2, #E5E6EB);
  background: var(--fill-2, #F2F3F5);
}

.card:hover {
  /* transform: translateY(-4px); */
  box-shadow: 0 8px 32px rgba(107, 117, 229, 0.1);
  /* border-color: #6b75e5; */
  /* background-color: #f2f3f5; */
}

.card-content {
  flex: 1;
  display: flex;
  width: 174px;
  height: 56px;
  flex-direction: column;
  justify-content: center;
  padding-right: 16px;
}

.card-title {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0px 0 8px 0;
  line-height: 1.3;
}

.card-subtitle {
  font-size: 14px;
  color: #6c757d;
  margin: 0;
  line-height: 1.5;
}

.card-image {
  position: absolute;
  top: 10px;
  right: 10px;
}

.card-image img {
  width: 225px;
  height: 97px;
  object-fit: contain;
}

.power-icon {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 100px;
  height: 100px;
}

.power-svg,
.power-gif {
  width: 100px;
  height: 100px;
  position: absolute;
  top: 0;
  left: 0;
  transition: opacity 0.3s ease;
}

.power-gif {
  opacity: 0;
}

.card:hover .power-svg {
  opacity: 0;
}

.card:hover .power-gif {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 1440px) {
  .cards-grid {
    grid-template-columns: repeat(2, 464px);
    grid-template-rows: repeat(3, 200px);
    gap: 24px;
  }
}

@media (max-width: 1024px) {
  .cards-grid {
    grid-template-columns: repeat(2, 350px);
    grid-template-rows: repeat(3, 180px);
    gap: 20px;
  }

  .card {
    width: 350px;
    height: 180px;
    padding: 20px;
  }

  .card-content {
    padding-right: 12px;
  }

  .card-image img {
    width: 180px;
    height: 78px;
  }

  .third-page-container {
    margin: 80px 24px;
    padding-bottom: 40px;
  }
}

@media (max-width: 768px) {
  .third-page-container {
    margin: 40px 12px 0 12px;
    width: calc(100% - 24px);
    overflow-x: auto;
    overflow-y: hidden;
    padding-bottom: 40px;
  }

  .cards-grid {
    display: flex;
    flex-direction: row;
    gap: 12px;
    padding: 0;
    width: max-content;
  }

  .card {
    width: 300px;
    height: 200px;
    padding: 24px;
    flex-shrink: 0;
    flex-direction: row;
    justify-content: space-between;
    box-sizing: border-box;
  }

  .card-content {
    flex: 1;
    display: flex;
    width: 174px;
    height: 56px;
    flex-direction: column;
    justify-content: center;
    padding-right: 16px;
  }

  .card-title {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0px 0 8px 0;
    line-height: 1.3;
  }

  .card-subtitle {
    font-size: 14px;
    color: #6c757d;
    margin: 0;
    line-height: 1.5;
  }

  .card-image {
    position: absolute;
    top: 10px;
    right: 10px;
  }

  .card-image img {
    width: 225px;
    height: 97px;
    object-fit: contain;
  }

  .power-icon {
    position: absolute;
    bottom: 10px;
    right: 10px;
    width: 100px;
    height: 100px;
  }
}

/* 小屏幕手机适配 */
@media (max-width: 480px) {
  .third-page-container {
    margin: 32px 8px 0 8px;
    width: calc(100% - 16px);
  }

  .cards-grid {
    gap: 8px;
  }

  .card {
    width: 280px;
    height: 180px;
    padding: 20px;
  }

  .card-content {
    width: 150px;
    height: 50px;
    padding-right: 12px;
  }

  .card-title {
    font-size: 18px;
  }

  .card-subtitle {
    font-size: 13px;
  }

  .card-image img {
    width: 200px;
    height: 85px;
  }

  .power-icon {
    width: 80px;
    height: 80px;
  }

  .power-svg,
  .power-gif {
    width: 80px;
    height: 80px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
  .third-page-container {
    margin: 28px 6px 0 6px;
    width: calc(100% - 12px);
  }

  .card {
    width: 260px;
    height: 160px;
    padding: 16px;
  }

  .card-content {
    width: 130px;
    height: 45px;
    padding-right: 10px;
  }

  .card-title {
    font-size: 16px;
  }

  .card-subtitle {
    font-size: 12px;
  }

  .card-image img {
    width: 180px;
    height: 75px;
  }

  .power-icon {
    width: 70px;
    height: 70px;
  }

  .power-svg,
  .power-gif {
    width: 70px;
    height: 70px;
  }
}
</style>