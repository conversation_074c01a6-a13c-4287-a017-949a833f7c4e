<template>
  <div class="login-container">
    <!-- 移动端顶部白色区域 -->
    <MobileHeader ref="mobileHeaderRef" />

    <div class="login-left-box" v-if="!mobileHeaderRef?.isMobile">
      <img src="../assets/copywriter.png" alt="Spacetime&AIOS" class="main-logo" />
    </div>
    <div class="login-right-box">
      <img src="../assets/logo.svg" alt="logo" class="logo" v-if="!mobileHeaderRef?.isMobile" />

      <div v-if="currentStep === 1">
        <div class="welcome-text">
          <span class="hello-text">验证身份</span>
        </div>

        <el-form :model="verifyForm" class="login-form">
          <el-form-item>
            <el-input
              v-model="verifyForm.phone"
              placeholder="手机号"
              class="form-input"
              size="large"
            />
          </el-form-item>

          <el-form-item>
            <div class="captcha-container">
              <el-input
                v-model="verifyForm.captcha"
                placeholder="验证码"
                class="form-input captcha-input"
                size="large"
              />
              <div class="captcha-image" @click="refreshCaptcha">
                <span class="captcha-text">{{ captchaCode }}</span>
              </div>
            </div>
          </el-form-item>

          <el-form-item>
            <el-input
              v-model="verifyForm.verifyCode"
              placeholder="验证码"
              class="form-input verify-code-input"
              size="large"
            >
              <template #suffix>
                <el-button
                  type="text"
                  class="send-code-btn"
                  @click="sendVerifyCode"
                >
                  发送验证码
                </el-button>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              class="login-btn"
              size="large"
              @click="handleNextStep"
            >
              下一步
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div v-else>
        <div class="welcome-text">
          <span class="hello-text">修改密码</span>
        </div>

        <el-form :model="passwordForm" class="login-form">
          <el-form-item>
            <el-input
              v-model="passwordForm.newPassword"
              type="password"
              placeholder="密码"
              class="form-input"
              size="large"
              show-password
            />
          </el-form-item>

          <el-form-item>
            <el-input
              v-model="passwordForm.confirmPassword"
              type="password"
              placeholder="确认密码"
              class="form-input"
              size="large"
              show-password
            />
          </el-form-item>

          <el-form-item>
            <div class="button-group">
              <el-button
                class="prev-btn"
                size="large"
                @click="handlePrevStep"
              >
                上一步
              </el-button>
              <el-button
                type="primary"
                class="submit-btn"
                size="large"
                @click="handleSubmit"
              >
                提交
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import MobileHeader from '../components/MobileHeader.vue'

const router = useRouter()

// 移动端头部组件引用
const mobileHeaderRef = ref<InstanceType<typeof MobileHeader>>()

//1-验证身份，2-修改密码
const currentStep = ref(1)

// 验证身份表单数据
const verifyForm = ref({
  phone: '',
  verifyCode: '',
  captcha: ''
})

// 修改密码表单数据
const passwordForm = ref({
  newPassword: '',
  confirmPassword: ''
})

// 图形验证码
const captchaCode = ref('JHWi')

// 刷新验证码
const refreshCaptcha = async () => {
  try {
    // TODO: 调用后端接口获取新的验证码
    // const response = await getCaptchaCode()
    // captchaCode.value = response.data.code

    // 临时生成随机验证码用于开发测试
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < 4; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    captchaCode.value = result
  } catch (error) {
    console.error('获取验证码失败:', error)
    ElMessage.error('获取验证码失败')
  }
}

// 发送验证码
const sendVerifyCode = () => {
  if (!verifyForm.value.phone) {
    ElMessage.warning('请输入手机号')
    return
  }
  // TODO: 调用发送验证码接口
  ElMessage.success('验证码已发送')
}

// 下一步
const handleNextStep = () => {
  if (verifyForm.value.phone !== '1') {
    ElMessage.warning('请输入手机号：1')
    return
  }
  if (verifyForm.value.verifyCode !== '2') {
    ElMessage.warning('请输入验证码：2')
    return
  }
  if (verifyForm.value.captcha !== '3') {
    ElMessage.warning('请输入图形验证码：3')
    return
  }

  // TODO: 验证身份接口调用
  // const response = await verifyIdentity({
  //   phone: verifyForm.value.phone,
  //   verifyCode: verifyForm.value.verifyCode,
  //   captcha: verifyForm.value.captcha
  // })

  ElMessage.success('验证成功')
  currentStep.value = 2
}

// 上一步
const handlePrevStep = () => {
  currentStep.value = 1
}

// 提交修改密码
const handleSubmit = () => {
  if (!passwordForm.value.newPassword) {
    ElMessage.warning('请输入新密码')
    return
  }
  if (!passwordForm.value.confirmPassword) {
    ElMessage.warning('请确认密码')
    return
  }
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    ElMessage.warning('两次输入的密码不一致')
    return
  }

  // TODO: 调用修改密码接口
  ElMessage.success('密码修改成功')
  // 在新标签页中打开登录页面
  router.push('/login')
}
</script>

<style scoped>
.login-container {
  margin: 10px;
  width: calc(100% - 20px);
  height: calc(100% - 20px);
  display: flex;
  flex-direction: row;
}

.login-left-box {
  background-image: url('../assets/banner.png');
  width: 1184px;
  height: 940px;
  background-size: cover;
  background-position: center;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}

.main-logo {
  width: 722px;
  height: 338px;
}

.login-right-box {
  display: flex;
  width: 706px;
  padding: 40px;
  flex-direction: column;
  flex-shrink: 0;
  align-self: stretch;
  border-radius: 20px;
  background: #ffffff;
}

.logo {
  height: 45px;
  width: auto;
  align-self: flex-start;
  margin-bottom: 60px;
}

.welcome-text {
  display: flex;
  flex-direction: column;
  margin-bottom: 40px;
}

.hello-text {
  color: var(--text-1, #1D2129);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 40px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
  margin-bottom: 8px;
}

.login-form {
  width: 100%;
}

.form-input {
  width: 100%;
  margin-bottom: 20px;
}

:deep(.form-input .el-input__wrapper) {
  background-color: #f2f3f5;
  border: 1px solid #E5E6EB;
  border-radius: 10px;
  padding: 0px 16px;
  height: 56px;
  box-shadow: none;
}

:deep(.form-input .el-input__wrapper.is-focus) {
  border-color: #2F7DFB;
  box-shadow: 0 0 0 2px rgba(47, 125, 251, 0.1);
}

:deep(.form-input .el-input__inner) {
  background: transparent;
  border: none;
  font-size: 16px;
  color: #1D2129;
}

:deep(.form-input .el-input__inner::placeholder) {
  color: #86909C;
}

/* 验证码输入框样式 */
.verify-code-input {
  position: relative;
}

:deep(.verify-code-input .el-input__suffix) {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  display: flex;
  align-items: center;
  padding-right: 16px;
}

.send-code-btn {
  color: #2F7DFB;
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  padding: 0;
  height: auto;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.send-code-btn:hover {
  opacity: 0.8;
}

/* 图形验证码样式 */
.captcha-container {
  display: flex;
  gap: 10px;
  width: 100%;
}

.captcha-input {
  flex: 1;
}

.captcha-image {
  width: 120px;
  height: 56px;
  background: #f0f0f0;
  border: 1px solid #E5E6EB;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  flex-shrink: 0;
  transition: border-color 0.3s ease;
}

.captcha-image:hover {
  border-color: #2F7DFB;
}

.captcha-text {
  font-size: 16px;
  color: #666;
  font-weight: bold;
  letter-spacing: 2px;
}

.login-btn {
  width: 100%;
  height: 60px;
  border-radius: 10px;
  font-size: 18px;
  font-family: "Alibaba PuHuiTi 2.0";
  border: 1px solid rgba(255, 255, 255, 0.20);
  background: linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%), linear-gradient(0deg, #2F7DFB 0%, #2F7DFB 100%), linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%);
  color: #FFF;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 1.8px;
}

/* 按钮组样式 */
.button-group {
  display: flex;
  gap: 10px;
  width: 100%;
}

.prev-btn {
  flex: 1;
  height: 60px;
  border-radius: 10px;
  background: var(--fill-3, #E5E6EB);
  border: none;
  color: #666;
  font-size: 18px;
  font-family: "Alibaba PuHuiTi 2.0";
  font-weight: 400;
  letter-spacing: 1.8px;
}

.prev-btn:hover {
  background: #d9dadf;
}

.submit-btn {
  flex: 1;
  height: 60px;
  border-radius: 10px;
  font-size: 18px;
  font-family: "Alibaba PuHuiTi 2.0";
  border: 1px solid rgba(255, 255, 255, 0.20);
  background: linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%), linear-gradient(0deg, #2F7DFB 0%, #2F7DFB 100%), linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%);
  color: #FFF;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 1.8px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
    margin: 0;
    width: 100%;
    height: 100vh;
  }

  .login-right-box {
    width: calc(100% - 24px);
    height: auto;
    min-height: calc(100vh - 104px);
    padding: 5% 5% 30px 5%;
    border-radius: 20px;
    margin: 104px 12px 12px 12px;
    background: #ffffff;
    flex: 1;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
  }

  .logo {
    height: 35px;
    margin-bottom: 30px;
  }

  .hello-text {
    font-size: 28px;
    margin-top: 0px;
  }

  .welcome-text {
    margin-bottom: 20px;
  }

  .login-form {
    width: 100%;
    flex: 1;
  }

  .form-input {
    width: 100%;
    margin-bottom: 20px;
  }

  :deep(.form-input .el-input__wrapper) {
    height: 50px;
    padding: 0 4%;
  }

  .login-btn {
    width: 100%;
    height: 50px;
    font-size: 16px;
    margin-top: 20px;
  }

  .button-group {
    margin-top: 20px;
  }

  .prev-btn,
  .submit-btn {
    height: 50px;
    font-size: 16px;
  }

  .captcha-container {
    gap: 3%;
  }

  .captcha-image {
    width: 25%;
    height: 50px;
    min-width: 80px;
  }

  .captcha-text {
    font-size: 14px;
  }

  .send-code-btn {
    font-size: 13px;
  }
}

/* 小屏幕手机适配 */
@media (max-width: 480px) {
  .login-right-box {
    padding: 4% 4% 20px 4%;
    margin: 88px 8px 8px 8px;
    min-height: calc(100vh - 88px);
  }

  .hello-text {
    font-size: 24px;
    margin-top: 0px;
  }

  .welcome-text {
    margin-bottom: 20px;
  }

  .form-input {
    width: 100%;
    margin-bottom: 20px;
  }

  :deep(.form-input .el-input__wrapper) {
    height: 48px;
    padding: 0 4%;
  }

  :deep(.form-input .el-input__inner) {
    font-size: 15px;
  }

  .login-btn {
    width: 100%;
    height: 48px;
    font-size: 15px;
    margin-top: 20px;
  }

  .button-group {
    margin-top: 20px;
  }

  .prev-btn,
  .submit-btn {
    height: 48px;
    font-size: 15px;
  }

  .captcha-container {
    gap: 3%;
  }

  .captcha-image {
    width: 25%;
    height: 48px;
    min-width: 70px;
  }

  .captcha-text {
    font-size: 12px;
  }

  .send-code-btn {
    font-size: 12px;
  }
}

@font-face {
  font-family: "江城斜黑体";
  src: url("../assets/typeface/JiangXieHei500W.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "江城斜黑体";
  src: url("../assets/typeface/JiangXieHei900W.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}
</style>