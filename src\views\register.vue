<template>
  <div class="login-container">
    <!-- 移动端顶部白色区域 -->
    <MobileHeader ref="mobileHeaderRef" />

    <div class="login-left-box" v-if="!mobileHeaderRef?.isMobile">
      <img src="../assets/copywriter.png" alt="Spacetime&AIOS" class="main-logo" />
    </div>
    <div class="login-right-box">
      <img src="../assets/logo.svg" alt="logo" class="logo" v-if="!mobileHeaderRef?.isMobile" />
      <div class="welcome-text">
        <span class="hello-text">Hello!</span>
        <span class="welcome-title">欢迎注册热爱元宇宙</span>
      </div>

      <el-form :model="registerForm" class="login-form">
        <!-- 住和角色 同一行 并且平分一行 -->
        <el-form-item>
          <div class="form-row house-role-form">
            <el-select v-model="registerForm.module" placeholder="住" class="form-input form-half" size="large" @change="onmoduleChange" popper-class="custom-dropdown">
              <el-option
                v-for="module in moduleOptions"
                :key="module.value"
                :label="module.label"
                :value="module.value"
              />
            </el-select>
            <el-select v-model="registerForm.role" placeholder="角色" class="form-input form-half" size="large" popper-class="custom-dropdown" :class="{ 'selected-role': registerForm.role }">
              <el-option
                v-for="role in roleOptions"
                :key="role.value"
                :label="role.label"
                :value="role.value"
              />
            </el-select>
          </div>
        </el-form-item>
        <el-form-item>
          <el-input v-model="registerForm.phone" placeholder="手机号" class="form-input" size="large"/>
        </el-form-item>
        <el-form-item>
          <el-input v-model="registerForm.password" type="password" placeholder="密码" class="form-input" size="large" show-password/>
        </el-form-item>
        <el-form-item>
          <el-input v-model="registerForm.confirmPassword" type="password" placeholder="确认密码" class="form-input" size="large" show-password/>
        </el-form-item>
        <el-form-item>
          <el-input v-model="registerForm.verifyCode" placeholder="验证码" class="form-input verify-code-input" size="large"
          >
            <template #suffix>
              <el-button type="text" class="send-code-btn" @click="sendVerifyCode">
                发送验证码
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="registerForm.inviteCode" placeholder="邀请码" class="form-input" size="large"/>
        </el-form-item>
        <el-form-item>
          <el-input v-model="registerForm.qwnershipPlatform" placeholder="归属平台店铺名称（非必填）" class="form-input" size="large"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" class="login-btn" size="large" @click="handleRegister">
            下一步
          </el-button>
        </el-form-item>

        <el-form-item>
          <div class="footer-links">
            <div class="agreement-text">
              <el-checkbox v-model="registerForm.agreeTerms" class="agreement-checkbox">
                我已阅读并同意<span class="agreement-link">《用户注册协议》</span>
              </el-checkbox>
            </div>
            <span class="link-text">已有账号？<span class="register-link" @click="goToLogin">去登录</span></span>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import MobileHeader from '../components/MobileHeader.vue'

const router = useRouter()

// 移动端头部组件引用
const mobileHeaderRef = ref<InstanceType<typeof MobileHeader>>()

// 注册表单数据
const registerForm = ref({
  module: 'residence',
  role: '',
  phone: '',
  password: '',
  confirmPassword: '',
  verifyCode: '',
  inviteCode: '',
  qwnershipPlatform: '',
  agreeTerms: false
})

const moduleOptions = ref([
  { label: '住', value: 'residence' },
  { label: '食', value: 'food' },
  { label: '衣', value: 'clothing' },
  { label: '行', value: 'transportation' },
  { label: '综合', value: 'comprehensive' }
])

// 角色数据映射
const roleMapping = {
  residence: [
    { label: '开发商', value: 'developer' },
    { label: '装饰公司/硬装设计公司', value: 'decoration_company' },
    { label: '软装设计公司', value: 'soft_decoration_company' },
    { label: '个人设计师', value: 'personal_designer' },
    { label: '建模师', value: 'modeler' },
    { label: '代理商', value: 'agent' },
    { label: '生产商', value: 'manufacturer' },
    { label: '材料商', value: 'material_supplier' },
    { label: '销售人员', value: 'sales_person' },
    { label: '安装人员', value: 'installer' },
    { label: '物流人员', value: 'logistics_person' },
    { label: '个人', value: 'module_individual' }
  ],
  food: [
    { label: '餐饮店', value: 'restaurant' },
    { label: '代理商', value: 'agent' },
    { label: '批发商', value: 'wholesaler' },
    { label: '生产商', value: 'manufacturer' },
    { label: '种植户', value: 'farmer_households' },
    { label: '养殖户', value: 'feed_households' },
    { label: '种源商', value: 'seed_source_supplier' },
    { label: '个人', value: 'food_individual' }
  ],
  clothing: [
    { label: '代理商', value: 'agent' },
    { label: '生产商', value: 'manufacturer' },
    { label: '设计师', value: 'designer' },
    { label: '材料商', value: 'material_supplier' },
    { label: '个人', value: 'clothing_individual' }
  ],
  transportation: [
    { label: '酒店/民宿', value: 'hotel_bnb' },
    { label: '酒店用品生产商', value: 'hotel_supplies_manufacturer' },
    { label: '个人', value: 'transportation_individual' }
  ],
  comprehensive: [
    { label: '软件开发人员', value: 'software_developer' },
    { label: '软件产品人员', value: 'software_product_person' },
    { label: '软件测试人员', value: 'software_test_person' },
    { label: '软件运维人员', value: 'software_ops_person' },
    { label: '软件项目管理人员', value: 'software_project_manager' },
    { label: 'UI设计师', value: 'ui_designer' },
    { label: '平面设计师', value: 'graphic_designer' },
    { label: '视频剪辑师', value: 'video_editor' },
    { label: '摄影师', value: 'photographer' },
    { label: '主播', value: 'streamer' },
    { label: '融媒体/自媒体人员', value: 'media_person' }
  ]
}

// 根据模块选择计算角色选项
const roleOptions = computed(() => {
  if (!registerForm.value.module) {
    return []
  }
  return roleMapping[registerForm.value.module as keyof typeof roleMapping] || []
})

// 模块改变时重置角色
const onmoduleChange = () => {
  registerForm.value.role = ''
}

// 发送验证码
const sendVerifyCode = () => {
  if (!registerForm.value.phone) {
    ElMessage.warning('请输入手机号')
    return
  }
  // 这里添加发送验证码的逻辑
  ElMessage.success('验证码已发送')
}

// 处理注册
const handleRegister = () => {
  // 表单验证
  if (!registerForm.value.module) {
    ElMessage.warning('请选择模块')
    return
  }
  if (!registerForm.value.role) {
    ElMessage.warning('请选择角色')
    return
  }
  if (!registerForm.value.phone) {
    ElMessage.warning('请输入手机号')
    return
  }
  if (!registerForm.value.password) {
    ElMessage.warning('请输入密码')
    return
  }
  if (!registerForm.value.confirmPassword) {
    ElMessage.warning('请确认密码')
    return
  }
  if (registerForm.value.password !== registerForm.value.confirmPassword) {
    ElMessage.warning('两次密码输入不一致')
    return
  }
  if (!registerForm.value.verifyCode) {
    ElMessage.warning('请输入验证码')
    return
  }
  if (!registerForm.value.agreeTerms) {
    ElMessage.warning('请阅读并同意用户注册协议')
    return
  }

  // 这里添加注册逻辑
  ElMessage.success('注册成功')
  // 在新标签页中打开登录页面
  router.push('/leaseagreement')
}

// 跳转到登录页面
const goToLogin = () => {
  // 在新标签页中打开登录页面
  router.push('/login')
}
</script>

<style scoped>
.login-container {
  margin: 10px;
  width: calc(100% - 20px);
  height: calc(100% - 20px);
  display: flex;
  flex-direction: row;
}

.login-left-box {
  background-image: url('../assets/banner.png');
  width: 1184px;
  height: 940px;
  background-size: cover;
  background-position: center;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}

.main-logo {
  width: 722px;
  height: 338px;
}

.login-right-box {
  display: flex;
  width: 706px;
  padding: 40px;
  flex-direction: column;
  flex-shrink: 0;
  align-self: stretch;
  border-radius: 20px;
  background: #ffffff;
}

.logo {
  height: 45px;
  width: auto;
  align-self: flex-start;
  margin-bottom: 40px;
}

.welcome-text {
  display: flex;
  flex-direction: column;
  margin-bottom: 40px;
}

.hello-text {
  color: var(--text-1, #1D2129);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 40px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
}

.welcome-title {
  color: var(--text-1, #1D2129);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 40px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
}

.login-form {
  width: 100%;
}

.form-input {
  width: 100%;
  height: 60px;
  margin-bottom:0px;
}

/* 表单行样式 */
.form-row {
  display: flex;
  gap: 10px;
  align-items: center;
  width: 100%;
}

/* 住和角色单独的样式 */
.house-role-form {
  display: flex;
}

/* 住下拉框：只针对模块选择框应用样式 */
:deep(.house-role-form .el-select:first-child .el-select__wrapper .el-select__selected-item) {
  color: var(--text-1, #1D2129) !important;
  font-family: "Alibaba PuHuiTi 2.0" !important;
  font-size: 16px !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: normal !important;
}

/* 角色下拉框：仅选中后应用指定样式 */
:deep(.selected-role .el-select__wrapper .el-select__selected-item) {
  color: var(--text-1, #1D2129) !important;
  font-family: "Alibaba PuHuiTi 2.0" !important;
  font-size: 16px !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: normal !important;
}

/* 角色选择框未选中时的占位符样式 */
:deep(.house-role-form .el-select:last-child:not(.selected-role) .el-select__placeholder) {
  color: #86909C !important;
  font-family: inherit !important;
  font-size: 16px !important;
}

.form-half {
  flex: 1;
  margin-bottom: 0;
}

:deep(.form-input .el-input__wrapper) {
  background-color: #f2f3f5;
  border: 1px solid #E5E6EB;
  border-radius: 10px;
  padding: 0px 16px;
  height: 56px;
  box-shadow: none;
}

:deep(.form-input .el-input__wrapper.is-focus) {
  border-color: #2F7DFB;
  box-shadow: 0 0 0 2px rgba(47, 125, 251, 0.1);
}

:deep(.form-input .el-input__inner) {
  background: transparent;
  border: none;
  font-size: 16px;
  color: #1D2129;
}

:deep(.form-input .el-input__inner::placeholder) {
  color: #86909C;
}

/* 下拉框样式 */
:deep(.form-input.el-select .el-select__wrapper) {
  background-color: #f2f3f5;
  border: 1px solid #E5E6EB;
  border-radius: 10px;
  padding: 0px 16px;
  height: 56px;
  box-shadow: none;
}

:deep(.form-input.el-select .el-select__wrapper.is-focused) {
  border-color: #2F7DFB;
  box-shadow: 0 0 0 2px rgba(47, 125, 251, 0.1);
}

:deep(.form-input.el-select .el-select__placeholder) {
  color: #86909C;
  font-size: 16px;
}

/* 自定义下拉框样式 */
:deep(.custom-dropdown) {
  border-radius: 10px !important;
  border: 1px solid var(--border-2, #E5E6EB) !important;
  background: #FFF !important;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.10) !important;
  display: flex !important;
  width: 301px !important;
  padding: 10px 0 !important;
  flex-direction: column !important;
  align-items: flex-start !important;
}

:deep(.custom-dropdown .el-select-dropdown__item) {
  padding: 8px 16px !important;
  font-size: 14px !important;
  color: #1D2129 !important;
  transition: background-color 0.3s ease !important;
}

:deep(.custom-dropdown .el-select-dropdown__item:hover) {
  background-color: #F2F3F5 !important;
}

:deep(.custom-dropdown .el-select-dropdown__item.is-selected) {
  background-color: #E8F4FF !important;
  color: #2F7DFB !important;
}

/* 验证码输入框样式 */
.verify-code-input {
  position: relative;
}

:deep(.verify-code-input .el-input__suffix) {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  display: flex;
  align-items: center;
  padding-right: 16px;
}

.send-code-btn {
  color: #2F7DFB;
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  padding: 0;
  height: auto;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.send-code-btn:hover {
  opacity: 0.8;
}

:deep(.send-code-btn.is-disabled) {
  color: #86909C;
  cursor: not-allowed;
}

.login-btn {
  width: 100%;
  height: 60px;
  border-radius: 10px;
  font-size: 18px;
  font-family: "Alibaba PuHuiTi 2.0";
  border: 1px solid rgba(255, 255, 255, 0.20);
  background: linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%), linear-gradient(0deg, #2F7DFB 0%, #2F7DFB 100%), linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%);
  color: #FFF;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 1.8px;
}

.footer-links {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  width: 100%;
}

.agreement-text {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.agreement-checkbox .el-checkbox__label) {
  color: var(--text-3, #86909C);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

/* 禁用勾选状态下的标签样式 */
:deep(.agreement-checkbox.is-checked .el-checkbox__label) {
  color: var(--text-3, #86909C) !important;
}

.agreement-link {
  color: var(--primary, #2F7DFB);
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.agreement-link:hover {
  opacity: 0.8;
}

.link-text {
  color: var(--text-3, #86909C);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  cursor: pointer;
  transition: color 0.3s ease;
}

.register-link {
  color: var(--primary, #2F7DFB);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  cursor: pointer;
  transition: color 0.3s ease;
}

.register-link:hover {
  opacity: 0.8;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
    margin: 0;
    width: 100%;
    height: 100vh;
    padding: 0;
  }

  .login-left-box {
    display: none;
  }

  .login-right-box {
    width: calc(100% - 24px);
    height: auto;
    min-height: calc(100vh - 120px);
    padding: 10px 20px 15px 20px;
    border-radius: 20px;
    margin: 120px 12px 8px 12px;
    background: #ffffff;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    box-sizing: border-box;
  }

  .logo {
    height: 35px;
    margin-bottom: 30px;
  }

  .hello-text,
  .welcome-title {
    font-size: 28px;
  }

  .welcome-text {
    margin-top: 5px;
    margin-bottom: 15px;
  }

  :deep(.form-input .el-input__wrapper) {
    height: 48px;
    padding: 8px 14px;
  }

  /* 减少表单项之间的间距 */
  :deep(.login-form .el-form-item) {
    margin-bottom: 12px;
  }

  .login-btn {
    height: 48px;
    font-size: 16px;
  }

  .form-input {
    width: 100%;
    box-sizing: border-box;
  }

  .login-form {
    width: 100%;
    box-sizing: border-box;
  }

  :deep(.form-input .el-input__wrapper) {
    box-sizing: border-box;
    width: 100%;
  }

  .footer-links {
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
    margin-bottom: 15px;
  }

  .agreement-text {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-right: auto; /* 强制靠左 */
  }

  /* 移动端隐藏登录链接 */
  .link-text {
    display: none;
  }

  .link-text {
    font-size: 13px;
  }

  .form-row {
    flex-direction: row;
    gap: 10px;
  }

  .form-half {
    flex: 1;
    width: calc(50% - 5px);
    box-sizing: border-box;
  }

  /* 下拉框在移动端的样式调整 */
  :deep(.form-input.el-select .el-select__wrapper) {
    box-sizing: border-box;
    width: 100%;
  }

  /* 自定义下拉框在移动端的宽度调整 */
  :deep(.custom-dropdown) {
    width: calc(100vw - 48px) !important;
    max-width: 350px !important;
  }
}

/* 小屏幕手机适配 */
@media (max-width: 480px) {
  .login-right-box {
    padding: 12px 16px 16px 16px;
    margin: 108px 8px 6px 8px;
    min-height: calc(100vh - 108px);
    justify-content: flex-start;
  }

  .hello-text,
  .welcome-title {
    font-size: 24px;
  }

  .welcome-text {
    margin-top: 8px;
    margin-bottom: 18px;
  }

  :deep(.form-input .el-input__wrapper) {
    height: 48px;
    padding: 8px 12px;
  }

  :deep(.form-input .el-input__inner) {
    font-size: 15px;
  }

  .login-btn {
    height: 48px;
    font-size: 15px;
  }

  .form-input {
    width: 100%;
    box-sizing: border-box;
  }

  .login-form {
    width: 100%;
    box-sizing: border-box;
  }

  /* 减少表单项之间的间距 */
  :deep(.login-form .el-form-item) {
    margin-bottom: 10px;
  }

  :deep(.form-input .el-input__wrapper) {
    box-sizing: border-box;
    width: 100%;
  }

  .form-row {
    flex-direction: row;
    gap: 10px;
  }

  .form-half {
    flex: 1;
    width: calc(50% - 5px);
    box-sizing: border-box;
  }

  .footer-links {
    flex-direction: column;
    gap: 15px;
  }

  /* 移动端隐藏登录链接 */
  .link-text {
    display: none;
  }

  /* 自定义下拉框在小屏幕的宽度调整 */
  :deep(.custom-dropdown) {
    width: calc(100vw - 32px) !important;
    max-width: 320px !important;
  }
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
  .login-right-box {
    padding: 8px 12px 12px 12px;
    margin: 98px 6px 4px 6px;
    min-height: calc(100vh - 98px);
    justify-content: flex-start;
  }

  .hello-text,
  .welcome-title {
    font-size: 20px;
  }

  .welcome-text {
    margin-top: 6px;
    margin-bottom: 15px;
  }

  :deep(.form-input .el-input__wrapper) {
    height: 44px;
    padding: 6px 10px;
  }

  :deep(.form-input .el-input__inner) {
    font-size: 14px;
  }

  .login-btn {
    height: 44px;
    font-size: 14px;
  }

  .form-input {
    width: 100%;
    box-sizing: border-box;
  }

  .login-form {
    width: 100%;
    box-sizing: border-box;
  }

  /* 减少表单项之间的间距 */
  :deep(.login-form .el-form-item) {
    margin-bottom: 8px;
  }

  :deep(.form-input .el-input__wrapper) {
    box-sizing: border-box;
    width: 100%;
  }

  .link-text {
    font-size: 12px;
  }

  .footer-links {
    margin-bottom: 30px;
  }

  /* 移动端隐藏登录链接 */
  .link-text {
    display: none;
  }

  .form-row {
    flex-direction: row;
    gap: 8px;
  }

  .form-half {
    flex: 1;
    width: calc(50% - 4px);
    box-sizing: border-box;
  }

  /* 自定义下拉框在超小屏幕的宽度调整 */
  :deep(.custom-dropdown) {
    width: calc(100vw - 24px) !important;
    max-width: 300px !important;
  }
}

@font-face {
  font-family: "江城斜黑体";
  src: url("../assets/typeface/JiangXieHei500W.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "江城斜黑体";
  src: url("../assets/typeface/JiangXieHei900W.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}
</style>